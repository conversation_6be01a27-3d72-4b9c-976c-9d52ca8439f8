import axios from "axios";
import config from "../config";

export const authService = {
  // API Service Methods
  userLoginService: (data) => {
    return axios.post(`${config.API.AUTH}/login`, data);
  },
  sessionValidationService: (data) => {
    return axios.post(`${config.API.FORGET}/validate_session`, data);
  },
  userRegisterService: (data) => {
    return axios.post(`${config.API.AUTH}/register`, data);
  },
  userForgetService: (data) => {
    return axios.post(`${config.API.FORGET}/forgot_password`, data);
  },
  userResetPasswordService: (data) => {
    return axios.post(`${config.API.FORGET}/reset_password`, data);
  },
  userLogoutService: (data) => {
    return axios.post(`${config.API.AUTH}/logout`, data);
  },
  userVerifyEmailService: (data) => {
    return axios.get(`${config.API.AUTH}/verify_email`, data);
  },
  userVerifyOTPService: (data) => {
    return axios.post(`${config.API.AUTH}/verify-otp`, data);
  },

  // Utility Methods (for backward compatibility)
  login: async (username, password, schoolCode = null) => {
    try {
      const requestData = { username, password };
      if (schoolCode) {
        requestData.main_code = schoolCode;
      }

      const response = await axios.post(`${config.API.AUTH}/login`, requestData, {
        withCredentials: true
      });

      if (response.data.token) {
        try {
          const base64Url = response.data.token.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));

          const payload = JSON.parse(jsonPayload);
          if (!payload.role) {
            throw new Error('Invalid token format: missing role');
          }

          const userData = response.data.user;
          if (userData && (userData.role === 'Teacher' || userData.role === 'Admin')) {
            if (userData.schoolcode && !userData.main_code) {
              userData.main_code = userData.schoolcode;
              delete userData.schoolcode;
            }
            if (userData.main_code) {
              localStorage.setItem(config.AUTH.SCHOOL_CODE_KEY, userData.main_code);
            }
          }

          localStorage.setItem(config.AUTH.TOKEN_KEY, response.data.token);
          localStorage.setItem(config.AUTH.USER_KEY, JSON.stringify(userData || response.data.user));
        } catch (e) {
          throw { error: 'Invalid token format. Please contact support.' };
        }
      }

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: error.error || 'Network error' };
    }
  },

  logout: () => {
    localStorage.removeItem(config.AUTH.TOKEN_KEY);
    localStorage.removeItem(config.AUTH.USER_KEY);
    localStorage.removeItem(config.AUTH.SCHOOL_CODE_KEY);
  },

  getCurrentUser: () => {
    const user = localStorage.getItem(config.AUTH.USER_KEY);
    if (!user) return null;

    const userData = JSON.parse(user);
    if (userData && (userData.role === 'Teacher' || userData.role === 'Admin')) {
      if (userData.schoolcode && !userData.main_code) {
        userData.main_code = userData.schoolcode;
        delete userData.schoolcode;
        localStorage.setItem(config.AUTH.USER_KEY, JSON.stringify(userData));
      }
    }

    return userData;
  },

  getToken: () => {
    return localStorage.getItem(config.AUTH.TOKEN_KEY);
  },

  isLoggedIn: () => {
    return !!localStorage.getItem(config.AUTH.TOKEN_KEY);
  },

  getAuthHeader: () => {
    const token = authService.getToken();
    if (token) {
      return { Authorization: `Bearer ${token}` };
    } else {
      return {};
    }
  },

  verifyToken: async () => {
    try {
      const token = authService.getToken();
      if (!token) {
        return { valid: false, error: 'No token found' };
      }

      try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        const payload = JSON.parse(jsonPayload);
        if (!payload.role) {
          authService.logout();
          return { valid: false, error: 'Invalid token format: missing role' };
        }
      } catch (e) {
        authService.logout();
        return { valid: false, error: 'Invalid token format' };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  },

  getSchoolCode: () => {
    const storedMainCode = localStorage.getItem(config.AUTH.SCHOOL_CODE_KEY);
    if (storedMainCode) {
      return storedMainCode;
    }

    const user = authService.getCurrentUser();
    if (!user) return null;

    return user.main_code || null;
  },
};
