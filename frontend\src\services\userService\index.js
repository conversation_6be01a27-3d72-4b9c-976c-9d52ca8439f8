import axios from "axios";
import config from "../../config";
import { authService } from "./authService";

export const userService = {
  // API Service Methods
  userRegisterService: (data) => {
    return axios.post(`${config.API.USERS}/register`, data);
  },
  userGetAllService: () => {
    return axios.get(`${config.API.USERS}/users`);
  },
  userGetByIdService: (userId) => {
    return axios.get(`${config.API.USERS}/users/${userId}`);
  },

  // Utility Methods (for backward compatibility)
  registerUser: async (userData) => {
    try {
      const response = await axios.post(`${config.API.USERS}/register`, userData, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getUsers: async () => {
    try {
      const response = await axios.get(`${config.API.USERS}/users`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 403) {
        return { users: [] };
      }
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getUser: async (userId) => {
    try {
      const response = await axios.get(`${config.API.USERS}/users/${userId}`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
};
