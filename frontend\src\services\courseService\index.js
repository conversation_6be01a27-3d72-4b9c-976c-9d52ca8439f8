import axios from "axios";
import config from "../../config";
import { authService } from "./authService";

export const courseService = {
  // API Service Methods
  courseCreateService: (data) => {
    return axios.post(`${config.API.COURSES}/courses`, data);
  },
  courseGetAllService: () => {
    return axios.get(`${config.API.COURSES}/courses`);
  },
  courseGetByIdService: (courseId) => {
    return axios.get(`${config.API.COURSES}/courses/${courseId}`);
  },
  courseMapStudentService: (data) => {
    return axios.post(`${config.API.COURSES}/map-student`, data);
  },
  courseGetStudentCoursesService: (studentId) => {
    return axios.get(`${config.API.COURSES}/student-courses/${studentId}`);
  },

  // Utility Methods (for backward compatibility)
  createCourse: async (courseData) => {
    try {
      const response = await axios.post(`${config.API.COURSES}/courses`, courseData, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getCourses: async () => {
    try {
      const response = await axios.get(`${config.API.COURSES}/courses`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      if (!error.response) {
        return { courses: [] };
      }
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getCourse: async (courseId) => {
    try {
      const response = await axios.get(`${config.API.COURSES}/courses/${courseId}`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  mapStudentToCourse: async (studentId, courseId) => {
    try {
      const response = await axios.post(`${config.API.COURSES}/map-student`, {
        student_id: studentId,
        course_id: courseId
      }, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getStudentCourses: async (studentId) => {
    try {
      const response = await axios.get(`${config.API.COURSES}/student-courses/${studentId}`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
};
